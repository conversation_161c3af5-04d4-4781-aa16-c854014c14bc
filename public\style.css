/* Fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@400;700&family=Open+Sans:wght@400;600&display=swap');

/* Reset browser styles */
* {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    vertical-align: baseline;
    box-sizing: border-box;
}

/* Global styles */
body {
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    background-color: #82a2c2;
    color: #333;
}

/* Color Variables */
:root {
    --primary-color: #6d7bab;
    --secondary-color: #575f8a;
    --background-color: #82a2c2;
    --text-color: #333;
    --text-color-light: #fff;
    --border-color: #ccc;
    --shadow-color: rgba(0,0,0,0.2);
}

/* Header styles */
header.SiteHeader {
    background-color: var(--primary-color);
    padding: 20px 0;
    text-align: center;
    color: white;
    box-shadow: 0 2px 4px var(--shadow-color);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

    header.SiteHeader h1 {
        font-family: 'Roboto Slab', serif;
        font-size: 4em;
        margin-bottom: 0.5em;
        font-weight: 700;
    }

.header-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0 20px;
    margin-bottom: 10px;
}

/* Position the 'About Us' link to the top-left corner */
.about-us-link, .how-it-works-link, .login-link {
    font-size: 1em;
    color: white;
    text-decoration: none;
    padding: 8px 15px;
    background: #445577;
    border-radius: 4px;
}

    .about-us-link:hover, .how-it-works-link:hover, .login-link:hover {
        background: #2c3e50;
        text-decoration: underline;
    }

/* Ensure About Us is at the top-left */
.about-us-link {
    position: absolute;
    top: 20px;
    left: 20px;
}

/* Align Login to the far right */
.login-link {
    margin-left: auto; /* Pushes Login to the far right */
}

.containerImg {
    width: 75%;
    float: right;
}

#missionStatement {
    font-size: 1.2em;
    color: white;
    margin-right: 20px;
    flex: 1;
}

/* Heading for game subject selection */
.game-subject-heading {
    flex: 1;
    font-size: 1.5em;
    color: #fff;
    text-align: left;
    padding: 10px;
    font-weight: 600;
}

/* Main content container */
.content-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    margin-top: 40px;
    padding: 0 20px;
}

/* Container styles */
.container {
    width: calc(33.33% - 20px);
    margin: 10px;
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 20px;
    box-shadow: 0 2px 4px var(--shadow-color);
    text-align: center;
}

/* Image wrapping styles */
.imgWrap {
    display: inline-block;
    border: 1px solid transparent;
    transition: transform 0.3s;
    width: 100%;
    max-height: 200px;
    overflow: hidden;
}

    .imgWrap img {
        width: 100%;
        height: auto;
    }

    .imgWrap:hover {
        transform: scale(1.05);
    }

/* Heading styles */
h1, h3 {
    font-family: 'Open Sans', sans-serif;
    margin-bottom: 0.5em;
    color: var(--text-color);
}

h1 {
    font-size: 2.5em;
    font-weight: 700;
}

h3 {
    font-size: 1.8em;
    font-weight: 600;
}

h4 {
    font-size: 1.3em;
    font-weight: 400;
    color: white;
}

/* Footer styles */
#FatFooter {
    background: rgba(44, 66, 91, 0.8);
    border-top: 1px solid black;
    font-size: 14px;
    padding: 20px;
    text-align: center;
    color: white;
}

/* Button styles */
.myButton {
    width: auto;
    height: auto;
    padding: 10px 20px;
    border: none;
    background-color: #003366;
    color: white;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    margin-top: 20px;
    text-transform: uppercase;
    font-weight: bold;
    box-shadow: 2px 4px var(--shadow-color);
    transition: background-color 0.3s;
}

    .myButton:hover {
        background-color: #002244;
    }

    .myButton:focus {
        outline: 2px solid #ffffff;
        outline-offset: 2px;
    }

.question-block, .video-container {
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ccc;
    text-align: center;
    display: block;
}

#questions-container {
    width: 100%;
    display: block;
}

.choices {
    margin-top: 10px;
    padding-left: 20px;
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

    .choices label {
        margin-right: 15px;
        margin-left: 15px;
    }

.wordWrapper2 {
    margin-top: 20px;
}


/* Button styles for predefined questions */
.question-button {
    margin: 5px;
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: var(--text-color-light);
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}


    .question-button:hover {
        background-color: #002244;
    }

    .question-button:focus {
        outline: 2px solid #ffffff;
        outline-offset: 2px;
    }

    .question-button:disabled {
        background-color: var(--border-color);
        cursor: not-allowed;
    }


.about-us-container {
    text-align: center;
    padding: 50px 20px;
    background-color: var(--background-color);
}

.profile-images {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 50px;
    margin-bottom: 30px;
}

.imageLeft {
    width: 160px;
    height: 160px;
    object-fit: cover;
    border-radius: 50%;
}

.imageRight {
    width: 155px;
    height: 155px;
    object-fit: cover;
    border-radius: 50%;
}

.about-us-text {
    max-width: 800px;
    margin: auto;
}

    .about-us-text h2 {
        font-size: 2.5em;
        margin-bottom: 1em;
    }

    .about-us-text p {
        font-size: 1em;
        line-height: 1.5;
        text-align: left;
        margin-bottom: 2em;
    }

.thank-you-container {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px var(--shadow-color);
    background-color: #fff;
    transition: transform 0.3s ease-in-out;
}

    .thank-you-container:hover {
        transform: scale(1.05);
    }

    .thank-you-container h1 {
        color: #333;
    }

    .thank-you-container p {
        color: #666;
    }

/* Custom styles for mobile responsiveness */
@media (max-width: 768px) {
    .imgToHover {
        width: 100%;
        height: auto;
    }

    .math-section h1, #missionStatement h3 {
        font-size: 18px;
    }

    .profile-images {
        flex-direction: column;
        gap: 10px;
    }
}

/* Media query for very small devices (mobile phones in portrait mode) */
@media (max-width: 576px) {
    .math-section h1 {
        font-size: 12px;
        line-height: 1.2;
        word-wrap: break-word;
        padding: 0 5px;
    }

    .container {
        padding: 0 5px;
    }

    .math-section {
        margin: 5px;
    }

    .imgWrap {
        margin: 0;
        padding: 0;
    }

    .imgToHover {
        max-width: 100%;
        height: auto;
        padding: 0;
    }

    .math-section {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .imgWrap {
        width: 100%;
        box-sizing: border-box;
    }

    .SiteHeader img {
        width: 50px;
        height: auto;
    }

    .profile-images {
        flex-direction: column;
        gap: 10px;
    }

    .about-us-link {
        top: 10px; /* Move it higher */
        left: 10px; /* Move it further left */
        padding: 6px 12px; /* Slightly reduce padding to fit better */
    }
}
