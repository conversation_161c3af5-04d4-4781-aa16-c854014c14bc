<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">14.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <Name>Maths-inCoding-by-Bun-Vercel</Name>
    <RootNamespace>Maths-inCoding-by-Bun-Vercel</RootNamespace>
  </PropertyGroup>
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>3dd4caa5-cb36-4679-a662-1ed1bca93399</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>
    </StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <ProjectTypeGuids>{3AF33F2E-1136-4D97-BBB7-1795711AC8B8};{349c5851-65df-11da-9384-00065b846f21};{9092AA53-FB77-4645-B42D-1CCCA6BD08BD}</ProjectTypeGuids>
    <NodejsPort>1337</NodejsPort>
    <StartWebBrowser>true</StartWebBrowser>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <Content Include=".env" />
    <Content Include=".gitattributes" />
    <Content Include=".gitignore" />
    <Content Include="api\chat.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\cosine_similarity.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\database.js" />
    <Content Include="api\general_page.js" />
    <Content Include="api\handle_questions.js" />
    <Content Include="api\mathsQuiz.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\login.js" />
    <Content Include="api\maths_questions.js" />
    <Content Include="api\repeat_teacher_dashboard.js" />
    <Content Include="api\submit_feedback.js" />
    <Content Include="api\teacher_dashboard.js" />
    <Content Include="api\videoPlayer.js" />
    <Content Include="config\index.js" />
    <Content Include="models\feedbackModel.js" />
    <Content Include="models\linkedPage.js" />
    <Content Include="models\mathQuestionsModel.js" />
    <Content Include="models\quizResultModel.js" />
    <Content Include="models\schoolModel.js" />
    <Content Include="models\studentModel.js" />
    <Content Include="models\teacherModel.js" />
    <Content Include="models\videoModel.js" />
    <Content Include="package-lock.json" />
    <Content Include="package.json" />
    <Content Include="public\about_us.html" />
    <Content Include="public\feedback_form.html" />
    <Content Include="public\how_it_works.html" />
    <Content Include="public\images\3D_game_engine.webp" />
    <Content Include="public\images\age11_12.webp" />
    <Content Include="public\images\age12_13.webp" />
    <Content Include="public\images\age13_14.webp" />
    <Content Include="public\images\age14_15.webp" />
    <Content Include="public\images\age15_16.webp" />
    <Content Include="public\images\age16_18.webp" />
    <Content Include="public\images\algebra_image.webp" />
    <Content Include="public\images\cave_engine.webp" />
    <Content Include="public\images\eggHatching.webp" />
    <Content Include="public\images\geometry.webp" />
    <Content Include="public\images\lemonoids.webp" />
    <Content Include="public\images\linux_site_logo.webp" />
    <Content Include="public\images\minecraft_java.webp" />
    <Content Include="public\images\number_image.webp" />
    <Content Include="public\images\physics_simulator.webp" />
    <Content Include="public\images\probability.webp" />
    <Content Include="public\images\projectile.webp" />
    <Content Include="public\images\question_images\angle_question_1.webp" />
    <Content Include="public\images\question_images\angle_question_2.webp" />
    <Content Include="public\images\question_images\angle_question_3.webp" />
    <Content Include="public\images\question_images\angle_question_4.webp" />
    <Content Include="public\images\question_images\angle_question_5.webp" />
    <Content Include="public\images\question_images\angle_question_6.webp" />
    <Content Include="public\images\question_images\independent_probability_1.webp" />
    <Content Include="public\images\question_images\independent_probability_2.webp" />
    <Content Include="public\images\question_images\independent_probability_3.webp" />
    <Content Include="public\images\question_images\independent_probability_4.webp" />
    <Content Include="public\images\question_images\independent_probability_5.webp" />
    <Content Include="public\images\question_images\independent_probability_6.webp" />
    <Content Include="public\images\question_images\mental_maths_1.webp" />
    <Content Include="public\images\question_images\mental_maths_2.webp" />
    <Content Include="public\images\question_images\mental_maths_3.webp" />
    <Content Include="public\images\question_images\mental_maths_4.webp" />
    <Content Include="public\images\question_images\mental_maths_5.webp" />
    <Content Include="public\images\question_images\mental_maths_6.webp" />
    <Content Include="public\images\question_images\ratiosharing.webp" />
    <Content Include="public\images\question_images\ratiosharing2.webp" />
    <Content Include="public\images\question_images\ratiosharing3.webp" />
    <Content Include="public\images\question_images\ratiosharing6.webp" />
    <Content Include="public\images\question_images\ratiosimplifying4.webp" />
    <Content Include="public\images\question_images\ratiosimplifying5.webp" />
    <Content Include="public\images\question_images\sinewave1.webp" />
    <Content Include="public\images\question_images\sinewave2.webp" />
    <Content Include="public\images\question_images\sinewave3.webp" />
    <Content Include="public\images\question_images\sinewave4.webp" />
    <Content Include="public\images\question_images\sinewave5.webp" />
    <Content Include="public\images\question_images\sinewave6.webp" />
    <Content Include="public\images\question_images\waterbucket1.webp" />
    <Content Include="public\images\question_images\waterbucket2.webp" />
    <Content Include="public\images\question_images\waterbucket3.webp" />
    <Content Include="public\images\question_images\waterbucket6.webp" />
    <Content Include="public\images\ratio2.webp" />
    <Content Include="public\images\roblox.webp" />
    <Content Include="public\images\Ryan.webp" />
    <Content Include="public\images\scratch.webp" />
    <Content Include="public\images\statistics.webp" />
    <Content Include="public\images\toby.webp" />
    <Content Include="public\images\visual_studio.webp" />
    <Content Include="public\images\water_bucket.webp" />
    <Content Include="public\login.html" />
    <Content Include="public\privacy-policy.html" />
    <Content Include="public\style.css" />
    <Content Include="public\teacher_dashboard.html" />
    <Content Include="public\terms-of-service.html" />
    <Content Include="public\VideoPlayer.css" />
    <Content Include="scripts\updateDatabase.js" />
    <Content Include="scripts\updateMathsQuestionVideoUrls.js" />
    <Content Include="scripts\updateVideoUrls.js" />
    <Content Include="scripts\uploadVideos.js" />
    <Content Include="seeds\layoutData.json" />
    <Content Include="seeds\mathsQuestionData.json" />
    <Content Include="seeds\seedSecondIteration.js" />
    <Content Include="seeds\videoData.json" />
    <Content Include="vercel.json" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="api\" />
    <Folder Include="config\" />
    <Folder Include="models\" />
    <Folder Include="public\" />
    <Folder Include="public\images\" />
    <Folder Include="public\images\question_images\" />
    <Folder Include="public\videos\" />
    <Folder Include="scripts\" />
    <Folder Include="seeds\" />
  </ItemGroup>
  <Import Project="$(VSToolsPath)\Node.js Tools\Microsoft.NodejsToolsV2.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:48022/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>True</UseCustomServer>
          <CustomServerUrl>http://localhost:1337</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}" User="">
        <WebProjectProperties>
          <StartPageUrl>
          </StartPageUrl>
          <StartAction>CurrentPage</StartAction>
          <AspNetDebugging>True</AspNetDebugging>
          <SilverlightDebugging>False</SilverlightDebugging>
          <NativeDebugging>False</NativeDebugging>
          <SQLDebugging>False</SQLDebugging>
          <ExternalProgram>
          </ExternalProgram>
          <StartExternalURL>
          </StartExternalURL>
          <StartCmdLineArguments>
          </StartCmdLineArguments>
          <StartWorkingDirectory>
          </StartWorkingDirectory>
          <EnableENC>False</EnableENC>
          <AlwaysStartWebServerOnDebug>False</AlwaysStartWebServerOnDebug>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>