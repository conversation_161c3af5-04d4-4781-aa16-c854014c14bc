<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Dashboard</title>
    <!-- Link to your webp icon for more specific cases if needed -->
    <link rel="icon" href="/images/linux_site_logo.webp" sizes="32x32" type="image/webp">

    <link href="/style.css" rel="stylesheet">
    <style>
        /* Styling similar to the login page, but adjusted for the dashboard */
        body {
            font-family: Arial, sans-serif;
        }

        .student-container {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
        }

        .quiz-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }

        .yellow {
            background-color: yellow;
        }

        .orange {
            background-color: orange;
        }

        .red {
            background-color: red;
        }

        .passed {
            color: green;
            font-weight: bold;
        }

        .failed {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <header class="SiteHeader">
        <h1>
            Maths inCoding
            <img style="float: right;" width="120" height="120" src="/images/linux_site_logo.webp" alt="Pi with numbers">
        </h1>
        <div id="missionStatement">
            <h3>... learning maths through coding computer games</h3>
        </div>
    </header>
    <!-- Dashboard Container -->
    <div id="dashboard">
        <h2>Teacher Dashboard</h2>
        <div id="results">Loading...</div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // Flag to track the first load
        let firstLoad = true;
        const teacherId = localStorage.getItem('teacherId');  // Retrieve teacherId from localStorage at the beginning

        // Function to fetch updated dashboard data
        async function fetchUpdatedDashboardData() {
            try {
                let dashboardResponse;

                if (firstLoad) {
                    // First load with authorization headers
                    const token = localStorage.getItem('token');
                    if (!token) {
                        alert('No valid token found. Please log in again.');
                        window.location.href = '/login.html';
                        return;
                    }

                    dashboardResponse = await fetch('/teacher_dashboard', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,  // Use the stored token
                        }
                    });

                    if (dashboardResponse.status === 401) {
                        alert('Session expired. Please log in again.');
                        localStorage.removeItem('token'); // Clear expired token
                        window.location.href = '/login.html';
                        return;
                    }

                    if (!dashboardResponse.ok) {
                        throw new Error('Unable to load dashboard');
                    }

                    const data = await dashboardResponse.json();

                    // Set teacherId in localStorage
                    localStorage.setItem('teacherId', data.teacherId);

                    // Update results HTML
                    document.getElementById('results').innerHTML = data.htmlContent;
                    firstLoad = false;
                } else {
                    const teacherId = localStorage.getItem('teacherId');
                    if (!teacherId) {
                        console.error('Teacher ID not found');
                        alert('Unable to load dashboard updates: Teacher ID missing');
                        return;
                    }

                    dashboardResponse = await fetch(`/repeat_teacher_dashboard?teacherId=${teacherId}`, {
                        method: 'GET',
                    });

                    if (!dashboardResponse.ok) {
                        throw new Error('Unable to load dashboard updates');
                    }

                    const resultsJson = await dashboardResponse.json();
                    document.getElementById('results').innerHTML = formatResultsHtml(resultsJson);
                }
            } catch (err) {
                console.error('Error fetching updated dashboard:', err);
                alert('Unable to fetch the updated dashboard: ' + err.message);
            }
        }

        // Helper function to format results into HTML
        function formatResultsHtml(results) {
            return Object.values(results).map(student => `
                <div class="student-container">
                    <div class="student-details">
                        <strong>${student.studentName} (${student.email})</strong>
                    </div>
                    <div class="quiz-results">
                        ${Object.entries(student.scores).map(([quizId, result]) => `
                            <div class="quiz-result ${result.count >= 3 ? 'red' : result.count === 2 ? 'orange' : 'yellow'}">
                                Quiz: ${quizId} - ${result.count} ${result.count === 1 ? 'failure' : 'failures'} - Last Date: ${result.dates[result.dates.length - 1]}
                            </div>
                        `).join('')}
                        ${student.passedResults.map(passed => `
                            <div class="quiz-result">
                                Quiz: ${passed.quizId} - Score: ${passed.score}% -
                                <span class="passed">Passed</span> - Date: ${passed.date}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        }

        // Initial fetch when the page loads
        fetchUpdatedDashboardData();

        // Set up periodic refresh every 10 seconds (10000 ms)
        setInterval(fetchUpdatedDashboardData, 10000);  // Refresh every 10 seconds
    </script>

</body>
</html>
