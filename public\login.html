<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Form</title>
    <link rel="icon" type="image/png" href="/images/linux_site_logo.webp" sizes="32x32">
    <link href="/style.css" rel="stylesheet">
    <style>
        /* Inline styling as per your original code */
        body { /* Inherits the font-family and other styles from your main stylesheet */ }
        .login-container {
            max-width: 600px;
            margin: 50px auto;
            background: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        .login-container h2 { text-align: center; margin-bottom: 30px; color: #333; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; font-weight: 600; margin-bottom: 10px; }
        .form-group input { width: 100%; padding: 15px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px; }
        button.myButton {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            margin-top: 20px;
            border-radius: 4px;
            box-shadow: none;
            transition: background-color 0.3s, box-shadow 0.3s;
        }
        button.myButton:hover { background-color: #575f8a; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); }
        /* Dashboard styles */
        .student-container { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 8px; background-color: #f9f9f9; }
        .quiz-result { margin-top: 10px; padding: 10px; border-radius: 4px; }
        .yellow { background-color: yellow; }
        .orange { background-color: orange; }
        .red { background-color: red; }
        .passed { color: green; font-weight: bold; }
        .failed { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <header class="SiteHeader">
        <h1>
            Maths inCoding
            <img style="float: right;" width="120" height="120" src="/images/linux_site_logo.webp" alt="Pi with numbers">
        </h1>
        <div id="missionStatement">
            <h3>... learning maths through coding computer games</h3>
        </div>
    </header>
    <div class="login-container">
        <h2>Login Form</h2>
        <form id="loginForm" method="POST" action="/api/login">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" placeholder="Your Email" required class="myInput">
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" placeholder="Your Password" required class="myInput">
            </div>
            <!-- Consent Section for Quiz Data Collection -->
            <div class="consent-section">
                <h3>Data Collection Consent</h3>
                <p>We collect personal information such as your quiz results to track your learning progress. This data will not be shared with third parties and will be stored securely. By clicking "I Accept", you agree to our <a href="/privacy-policy.html">Privacy Policy</a> and <a href="/terms-of-service.html">Terms of Service</a>.</p>
                <input type="checkbox" id="consentCheckbox" required>
                <label for="consentCheckbox">I Accept</label>
            </div>
            <button type="submit" class="myButton">Login</button>
        </form>
    </div>
    <!-- Teacher Dashboard Container -->
    <div id="dashboard" class="login-container" style="display: none;">
        <h2>Teacher Dashboard</h2>
        <div id="results"></div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const consentCheckbox = document.getElementById('consentCheckbox');

            // Check if the user has accepted the consent
            if (!consentCheckbox.checked) {
                alert('You must accept the data collection consent to log in.');
                return;  // Prevent login if consent not given
            }

            try {
                const response = await axios.post('/api/login', { email, password });

                // Store the JWT token for future use
                localStorage.setItem('token', response.data.token);
                if (response.data.user.role === 'teacher') {
                    // Redirect to the dashboard page
                    window.location.href = '/teacher_dashboard.html';
                } else {
                    window.location.href = '/';
                }
            } catch (err) {
                alert('Login failed: ' + (err.response?.data?.message || 'Unknown error'));
            }
        });

  
    </script>
</body>
</html>
