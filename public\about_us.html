<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - Maths inCoding</title>
    <link rel="icon" type="image/png" href="/images/linux_site_logo.webp" sizes="32x32">
    <link href="/style.css" rel="stylesheet">
</head>
<body>
    <header class="SiteHeader">
        <h1>
            Maths inCoding
            <img style="float: right;" width="120" height="120" src="/images/linux_site_logo.webp" alt="Pi with numbers">
        </h1>
        <div id="missionStatement">
            <h3>... learning maths through coding computer games</h3>
        </div>
    </header>

    <div class="about-us-container">
        <!-- Section for profile images -->
        <div class="profile-images">
            <img src="/images/Ryan.webp" alt="Profile Image 1" class="imageLeft">
            <img src="/images/toby.webp" alt="Profile Image 2" class="imageRight">
        </div>

        <!-- Section for writing about the project -->
        <div class="about-us-text">
            <h2>About Maths inCoding</h2>
            <p>
                Ryan (the one who looks like I have cloned Bill Gates) is a web developer and Toby (the one who doesn't) is a maths
                and programming teacher who have combined to teach students maths through coding computer games.
            </p>
            <p>
                Toby has taught maths to students inside and outside of the school system and noticed that many who wouldn't learn from textbooks, would 
                learn maths to code their computer games. Ryan has guided the building of this website,
                a website to help students learn mathematics while making games they want to show off and play.            
            </p>

        </div>
    </div>

    <footer id="FatFooter">
        <a href="/feedback_form.html">
            <div class="wordWrapper2">
                <h4>If you have any queries about the information presented here, please click this link</h4>
            </div>
        </a>
    </footer>
</body>
</html>
