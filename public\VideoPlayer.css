/* Reset browser styles */
* {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    vertical-align: baseline;
    box-sizing: border-box;
 
}


/* Global styles - Match the filtering page */
body {
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    background-color: #82a2c2; /* Same as the filtering page */
    color: #333; /* Same as the filtering page */
}

/* Mission statement styles to match the header subtext */
#missionStatement {
    font-size: 1.2em;
    color: white;
    margin-right: 20px;
    flex: 1;
}



/* Header styles - Assuming you have a header on the video player page */
header.SiteHeader {
    background: #6d7bab;
    padding: 20px 0;
    text-align: center;
    color: black;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column; /* Stack children vertically */
 
}

    header.SiteHeader h1 {
        font-family: 'Roboto Slab', serif;
        font-size: 4em;
        margin-bottom: 0.5em;
        font-weight: 700;
    }

.header-bottom {
    align-self: flex-start; /* Aligns the navigation to the left */
    margin-left: 20px; /* Add margin to align with the rest of the content */
    margin-bottom: 10px; /* Space from the bottom of the header */
}

/* Image and heading container */
.image-and-heading-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
}





/* Video container should match the content container from the filtering page */
.video-container {
    width: 75%; /* Increase width to make the video larger */
    /* Do not set a height here to allow the aspect ratio to control the video size */
    padding-top: 56.25%; /* This will create an aspect ratio of 16:9 */
    position: relative;
    margin: 20px auto; /* Center the container with auto margins */
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    overflow: hidden; /* Hide overflow to keep everything neat */
}

    .video-container video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%; /* Video will fill the width */
        height: calc(100% - 40px); /* Subtract the height of the controls */
    }

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%; /* Controls span the width of the container */
    padding: 10px;
    background: #6d7bab; /* Match the site header color */
    box-sizing: border-box; /* Include padding in the element's total width and height */
}


/* Seek bar should match the button color scheme from the filtering page */
.seek {
    cursor: pointer;
    margin: 0;
}

/* Button styles - Match the button styles from the filtering page */
button {
    cursor: pointer;
    margin-right: 7px;
    font-size: 12px;
    padding: 10px; /* Adjusted padding to match the filtering page button sizes */
    border: none;
    outline: none;
    background-color: #6d7bab; /* Same as the header background from the filtering page */
    color: white;
    font-family: 'Open Sans', sans-serif;
    text-transform: uppercase; /* Assuming button text is uppercase on the filtering page */
    border-radius: 5px; /* Assuming buttons have a border-radius on the filtering page */
    transition: background-color 0.3s; /* For a smooth hover effect */
}

    button:hover {
        background-color: #6d7bab; /* Darken the button on hover */
    }




/* Feedback link */
.wordWrapper2 {
    margin-top: 20px;
}

.description-container {
    width: 100%; /* Full width */
    text-align: center; /* Center text within the container */
    margin: 0 auto; /* Center container in its parent if necessary */
}



/* Media query for small devices */
@media (max-width: 576px) {
    /* Additional responsive adjustments for small devices */
    .video-container {
        padding-top: 75%; /* Adjust the padding-top value for a 4:3 Aspect Ratio, if needed */
    }

        .video-container video {
            width: 100%;
            height: auto; /* Adjust height automatically to maintain aspect ratio */
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%); /* Center the video in the container */
        }

    /* Adjust titles and images for small screens */
    .math-section h1 {
        font-size: 14px; /* Decrease font size for titles */
        margin: 0;
        padding: 5px;
    }

    .imgToHover {
        width: 100%;
        height: auto;
    }

    /* Ensure the SiteHeader scales correctly */
    .SiteHeader h1 {
        font-size: 16px;
        text-align: center;
    }

    /* Ensure the SiteHeader image scales correctly */
    .SiteHeader img {
        width: 50px;
        height: auto;
    }

    /* Adjust footer images for small screens */
    .footerImgOne img, .footerImgTwo img, .footerImgThree img, .footerImgFour img {
        width: 80%; /* Slightly reduce image width */
        height: auto;
        margin: 0 auto; /* Center the images within their container */
        display: block; /* Ensures that images are block-level for margin auto to work */
    }


    /* Adjust font size in the footer for small screens */
    .wordWrapper2 h4 {
        font-size: 12px; /* Reduce the font size for the footer text */
    }
}
